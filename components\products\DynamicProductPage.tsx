"use client"

import React, { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { AppHeader } from "@/components/layout/AppHeader"
import { SideMenu } from "@/components/layout/SideMenu"
import { MobileNavigation } from "@/components/layout/MobileNavigation"
import { DesktopFooter } from "@/components/layout/DesktopFooter"
import {
  ArrowLeft,
  Star,
  Clock,
  Zap,
  Shield,
  Check,
  Gift,
  Sparkles,
  Key,
  Download
} from "lucide-react"
import { ProductTemplate, ProductFormData } from "@/lib/types"
import { getProductById } from "@/lib/services/productService"
import { InteractiveProductForm } from "./InteractiveProductForm"

interface DynamicProductPageProps {
  productId: string
}

export function DynamicProductPage({ productId }: DynamicProductPageProps) {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("shop")
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [product, setProduct] = useState<ProductTemplate | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Load product from the new product management system
  useEffect(() => {
    loadProduct()
  }, [productId])

  /**
   * ## TODO: Replace with Supabase query
   * Load product by ID
   */
  const loadProduct = async () => {
    try {
      setLoading(true)
      setError(null)
      const productData = await getProductById(productId)

      if (!productData) {
        setError("المنتج غير موجود")
        return
      }

      if (!productData.isActive) {
        setError("هذا المنتج غير متاح حالياً")
        return
      }

      setProduct(productData)
    } catch (error) {
      console.error("Error loading product:", error)
      setError("حدث خطأ أثناء تحميل المنتج")
    } finally {
      setLoading(false)
    }
  }

  // Navigation handler
  const handleTabChange = (tab: string) => {
    if (tab === "wallet") {
      router.push("/wallet")
    } else if (tab === "profile") {
      router.push("/profile")
    } else if (tab === "shop") {
      router.push("/shop")
    } else if (tab === "home") {
      router.push("/")
    } else if (tab === "support") {
      router.push("/contact")
    } else {
      setActiveTab(tab)
    }
  }

  // Handle form submission (purchase)
  const handlePurchase = async (formData: ProductFormData) => {
    try {
      console.log('Purchase data:', formData)
      // ## TODO: Implement actual purchase logic with Supabase
      // For now, redirect to checkout or show success message
      router.push('/checkout/success')
    } catch (error) {
      console.error("Error processing purchase:", error)
      alert("حدث خطأ أثناء معالجة الطلب")
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">⏳</div>
          <h3 className="text-xl font-bold text-white mb-2">جاري تحميل المنتج...</h3>
          <p className="text-slate-400">يرجى الانتظار</p>
        </div>
      </div>
    )
  }

  if (error || !product) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">❌</div>
          <h3 className="text-xl font-bold text-white mb-2">
            {error || "المنتج غير موجود"}
          </h3>
          <p className="text-slate-400 mb-6">
            {error || "لم نتمكن من العثور على المنتج المطلوب"}
          </p>
          <Button onClick={() => router.push('/shop')} className="bg-yellow-500 hover:bg-yellow-600 text-slate-900">
            العودة للمتجر
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white">
      {/* Background Pattern */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-400/10 rounded-full blur-3xl" />
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-orange-500/10 rounded-full blur-3xl" />
      </div>

      <AppHeader onMenuOpen={() => setIsMenuOpen(true)} />
      <SideMenu isOpen={isMenuOpen} onClose={() => setIsMenuOpen(false)} />

      <main className="container mx-auto px-4 py-8 relative z-10">
        {/* Back Button */}
        <Button
          variant="ghost"
          onClick={() => router.back()}
          className="mb-6 text-slate-300 hover:text-white hover:bg-slate-800/50"
        >
          <ArrowLeft className="h-4 w-4 ml-2" />
          العودة
        </Button>

        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            
            {/* Left Column - Product Image & Info */}
            <div className="space-y-6">
              {/* Product Image */}
              <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm overflow-hidden">
                <CardContent className="p-0">
                  <div className="relative aspect-square">
                    {product.previewImage ? (
                      <img
                        src={product.previewImage}
                        alt={product.name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full bg-gradient-to-br from-slate-700 via-slate-600 to-slate-800 flex items-center justify-center">
                        <Gift className="h-24 w-24 text-slate-400" />
                      </div>
                    )}
                    
                    {/* Badges */}
                    <div className="absolute top-4 right-4 space-y-2">
                      {/* Digital Product Badge */}
                      {product.productType === "digital" && (
                        <Badge className="bg-gradient-to-r from-purple-500 to-pink-500 text-white font-bold animate-pulse">
                          <Sparkles className="h-3 w-3 mr-1" />
                          منتج رقمي
                        </Badge>
                      )}
                      
                      <Badge
                        className={`${
                          product.processingType === "instant"
                            ? 'bg-green-500/20 text-green-400 border-green-500/30'
                            : 'bg-blue-500/20 text-blue-400 border-blue-500/30'
                        }`}
                      >
                        {product.processingType === "instant" ? (
                          <>
                            <Zap className="h-3 w-3 mr-1" />
                            {product.estimatedTime}
                          </>
                        ) : (
                          <>
                            <Clock className="h-3 w-3 mr-1" />
                            {product.estimatedTime}
                          </>
                        )}
                      </Badge>

                      {product.productType === "digital" && (
                        <Badge className="bg-purple-500/20 text-purple-400 border-purple-500/30">
                          <Download className="h-3 w-3 mr-1" />
                          منتج رقمي
                        </Badge>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Product Features */}
              <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
                <CardContent className="p-6">
                  <h3 className="text-lg font-bold text-white mb-4 flex items-center gap-2">
                    <Shield className="h-5 w-5 text-green-400" />
                    مميزات المنتج
                  </h3>
                  <div className="space-y-3">
                    {product.features && product.features.length > 0 ? (
                      product.features.map((feature, index) => (
                        <div key={index} className="flex items-center gap-3">
                          <Check className="h-4 w-4 text-green-400 flex-shrink-0" />
                          <span className="text-slate-300 text-sm">{feature}</span>
                        </div>
                      ))
                    ) : (
                      // Default features based on product type
                      <>
                        {product.productType === "digital" ? (
                          <>
                            <div className="flex items-center gap-3">
                              <Check className="h-4 w-4 text-green-400 flex-shrink-0" />
                              <span className="text-slate-300 text-sm">🚀 تسليم فوري للمحتوى الرقمي</span>
                            </div>
                            <div className="flex items-center gap-3">
                              <Check className="h-4 w-4 text-green-400 flex-shrink-0" />
                              <span className="text-slate-300 text-sm">🔒 محتوى محمي ومشفر</span>
                            </div>
                          </>
                        ) : (
                          <>
                            <div className="flex items-center gap-3">
                              <Check className="h-4 w-4 text-green-400 flex-shrink-0" />
                              <span className="text-slate-300 text-sm">⚡ شحن سريع وآمن</span>
                            </div>
                            <div className="flex items-center gap-3">
                              <Check className="h-4 w-4 text-green-400 flex-shrink-0" />
                              <span className="text-slate-300 text-sm">🛡️ ضمان الجودة</span>
                            </div>
                          </>
                        )}
                        <div className="flex items-center gap-3">
                          <Check className="h-4 w-4 text-green-400 flex-shrink-0" />
                          <span className="text-slate-300 text-sm">💬 دعم فني متخصص</span>
                        </div>
                      </>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Right Column - Interactive Product Form */}
            <div className="space-y-6">
              <InteractiveProductForm
                template={product}
                onSubmit={handlePurchase}
                currency="USD" // ## TODO: Get from user preferences
                showPricing={true}
              />
            </div>
          </div>
        </div>
      </main>

      <MobileNavigation activeTab={activeTab} onTabChange={handleTabChange} />
      <DesktopFooter activeTab={activeTab} onTabChange={handleTabChange} />
    </div>
  )
}
