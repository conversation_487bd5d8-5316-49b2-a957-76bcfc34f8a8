"use client"

import React, { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { getProducts, getProductById } from "@/lib/services/productService"
import { forceReinitializeTemplates } from "@/lib/data/defaultProductTemplates"
import { ProductTemplate } from "@/lib/types"
import Link from "next/link"

export default function ProductDebugPage() {
  const [products, setProducts] = useState<ProductTemplate[]>([])
  const [loading, setLoading] = useState(true)
  const [testProductId, setTestProductId] = useState("")
  const [testResult, setTestResult] = useState<string | null>(null)

  useEffect(() => {
    loadProducts()
  }, [])

  const loadProducts = async () => {
    try {
      setLoading(true)
      const productList = await getProducts()
      setProducts(productList)
      console.log("Loaded products:", productList)
    } catch (error) {
      console.error("Error loading products:", error)
    } finally {
      setLoading(false)
    }
  }

  const handleReinitialize = () => {
    forceReinitializeTemplates()
    loadProducts()
  }

  const testProductById = async () => {
    if (!testProductId.trim()) return
    
    try {
      const product = await getProductById(testProductId.trim())
      if (product) {
        setTestResult(`✅ Found: ${product.name} (Active: ${product.isActive})`)
      } else {
        setTestResult(`❌ Product not found: ${testProductId}`)
      }
    } catch (error) {
      setTestResult(`🔥 Error: ${error}`)
    }
  }

  const clearLocalStorage = () => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('productTemplates')
      setTestResult("🗑️ localStorage cleared")
      loadProducts()
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white p-6">
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-yellow-400 mb-2">Product Debug Panel</h1>
          <p className="text-slate-400">Debug and test product loading functionality</p>
        </div>

        {/* Control Panel */}
        <Card className="bg-slate-800/50 border-slate-700/50 mb-6">
          <CardHeader>
            <CardTitle className="text-yellow-400">Controls</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex gap-4 flex-wrap">
              <Button onClick={loadProducts} className="bg-blue-600 hover:bg-blue-700">
                🔄 Reload Products
              </Button>
              <Button onClick={handleReinitialize} className="bg-green-600 hover:bg-green-700">
                🔧 Force Reinitialize
              </Button>
              <Button onClick={clearLocalStorage} className="bg-red-600 hover:bg-red-700">
                🗑️ Clear localStorage
              </Button>
            </div>
            
            <div className="flex gap-2 items-center">
              <input
                type="text"
                value={testProductId}
                onChange={(e) => setTestProductId(e.target.value)}
                placeholder="Enter product ID to test..."
                className="flex-1 px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white"
              />
              <Button onClick={testProductById} className="bg-purple-600 hover:bg-purple-700">
                🔍 Test ID
              </Button>
            </div>
            
            {testResult && (
              <div className="p-3 bg-slate-700/50 rounded border border-slate-600">
                <p className="text-sm">{testResult}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Products List */}
        <Card className="bg-slate-800/50 border-slate-700/50">
          <CardHeader>
            <CardTitle className="text-yellow-400">
              Available Products ({products.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <p className="text-slate-400">Loading products...</p>
            ) : products.length === 0 ? (
              <p className="text-red-400">No products found!</p>
            ) : (
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {products.map((product) => (
                  <Card key={product.id} className="bg-slate-700/50 border-slate-600/50">
                    <CardContent className="p-4">
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <Badge variant={product.isActive ? "default" : "destructive"}>
                            {product.isActive ? "Active" : "Inactive"}
                          </Badge>
                          {product.isFeatured && (
                            <Badge variant="outline" className="text-yellow-400 border-yellow-400">
                              Featured
                            </Badge>
                          )}
                        </div>
                        
                        <h3 className="font-bold text-white">{product.name}</h3>
                        <p className="text-sm text-slate-400">{product.nameEnglish}</p>
                        <p className="text-xs text-slate-500">ID: {product.id}</p>
                        <p className="text-xs text-slate-500">Category: {product.category}</p>
                        <p className="text-xs text-slate-500">Packages: {product.packages?.length || 0}</p>
                        
                        <div className="flex gap-2 mt-3">
                          <Link href={`/shop/${product.id}`}>
                            <Button size="sm" className="bg-yellow-600 hover:bg-yellow-700 text-xs">
                              View Shop
                            </Button>
                          </Link>
                          <Link href={`/products/${product.id}`}>
                            <Button size="sm" variant="outline" className="text-xs">
                              View Product
                            </Button>
                          </Link>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Quick Links */}
        <Card className="bg-slate-800/50 border-slate-700/50 mt-6">
          <CardHeader>
            <CardTitle className="text-yellow-400">Quick Test Links</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-2 md:grid-cols-2 lg:grid-cols-4">
              <Link href="/shop/pubg-mobile-uc">
                <Button variant="outline" className="w-full text-xs">
                  PUBG Mobile UC
                </Button>
              </Link>
              <Link href="/shop/free-fire-diamonds">
                <Button variant="outline" className="w-full text-xs">
                  Free Fire Diamonds
                </Button>
              </Link>
              <Link href="/shop/google-play-gift-card">
                <Button variant="outline" className="w-full text-xs">
                  Google Play Card
                </Button>
              </Link>
              <Link href="/shop/tiktok-coins">
                <Button variant="outline" className="w-full text-xs">
                  TikTok Coins
                </Button>
              </Link>
              <Link href="/shop/invalid-product">
                <Button variant="destructive" className="w-full text-xs">
                  Invalid Product (Test Error)
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
